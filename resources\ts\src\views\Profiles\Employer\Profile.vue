<template>
    <h2 class="text-2xl font-bold my-4">Profile Settings</h2>
    <div v-if="loading" class="text-center py-6">
        <p class="text-gray-600">Loading...</p>
    </div>
    <div v-else-if="error" class="text-danger text-center py-6">
        <p>{{ error }}</p>
    </div>
    <div v-else-if="employer">
        <div class="card border">
            <!-- Basic Information -->
            <div class="card-header">
                <div class="card-title">
                    <h3 class="fw-normal">Basic Information</h3>
                </div>
                <button type="button" class="btn btn-sm btn-secondary align-self-center" @click="toggleEditMode">
                    {{ isEditMode ? 'Save Details' : 'Update Details' }}
                </button>
            </div>
            <div class="card-body">
                <form @submit.prevent="updateEmployer" class="text-black">
                    <div class="row mb-3">
                        <label class="col-sm-4 col-form-label">First Name</label>
                        <div class="col-sm-8">
                            <p v-if="!isEditMode" class="form-control-plaintext text-right">{{
                                employer.first_name || 'N/A' }}</p>
                            <input v-else type="text" class="form-control bg-light fw-bold" v-model="employer.first_name" />
                        </div>
                    </div>
                    <div class="row mb-3">
                        <label class="col-sm-4 col-form-label">Last Name</label>
                        <div class="col-sm-8">
                            <p v-if="!isEditMode" class="form-control-plaintext text-right">{{
                                employer.last_name || 'N/A' }}</p>
                            <input v-else type="text" class="form-control bg-light fw-bold" v-model="employer.last_name" />
                        </div>
                    </div>
                    <div class="row mb-3">
                        <label class="col-sm-4 col-form-label">Position</label>
                        <div class="col-sm-8">
                            <p v-if="!isEditMode" class="form-control-plaintext text-right">{{
                                employer.profile.position || 'N/A' }}</p>
                            <input v-else type="text" class="form-control bg-light fw-bold" v-model="employer.profile.position" />
                        </div>
                    </div>
                    <div class="row mb-3">
                        <label class="col-sm-4 col-form-label">Company</label>
                        <div class="col-sm-8">
                            <p v-if="!isEditMode" class="form-control-plaintext text-right">{{
                                employer.company_name || 'N/A' }}</p>
                            <Field v-else as="select" name="company_id" v-model="employer.company_id" class="form-control bg-light fw-bold">
                                <option value="">Select Company</option>
                                <option v-for="company in companieslist" :key="company.value" :value="company.value">
                                    {{ company.label }}
                                </option>
                            </Field>
                            <!-- <Multiselect v-else class="rounded form-control bg-light fw-bold" :searchable="false" placeholder="Select Company" noOptionsText="No companies available" :options="companieslist" v-model="employer.company_id" @select="handleCompanyChange" @clear="clearCompanySelection"> </Multiselect> -->
                        </div>
                    </div>
                    <div class="row mb-3">
                        <label class="col-sm-4 col-form-label">Phone Number</label>
                        <div class="col-sm-8">
                            <p v-if="!isEditMode" class="form-control-plaintext text-right">{{
                                employer.profile.phone || 'N/A' }}</p>
                            <input v-else type="text" class="form-control bg-light fw-bold" v-model="employer.profile.phone" />
                        </div>
                    </div>
                    <div class="row mb-3">
                        <label class="col-sm-4 col-form-label">Email</label>
                        <div class="col-sm-8">
                            <p v-if="!isEditMode" class="form-control-plaintext text-right">{{
                                employer.email || 'N/A' }}</p>
                            <input v-else type="email" class="form-control bg-light fw-bold" v-model="employer.email" />
                        </div>
                    </div>
                    <div class="row mb-3">
                        <label class="col-sm-4 col-form-label">State</label>
                        <div class="col-sm-8">
                            <p v-if="!isEditMode" class="form-control-plaintext text-right">{{
                                employer.state || 'N/A' }}</p>
                            <Field v-else as="select" name="state_id" v-model="employer.state_id" class="form-control bg-light fw-bold">
                                <option value="">Select State</option>
                                <option v-for="state in stateslist" :key="state.value" :value="state.value">
                                    {{ state.label }}
                                </option>
                            </Field>
                            <!-- <Multiselect v-else class="rounded form-control bg-light fw-bold" :searchable="false" placeholder="Select State" noOptionsText="No states available" :options="stateslist" v-model="employer.state_id" @select="handleStateChange" @clear="clearStateSelection"> </Multiselect> -->
                        </div>
                    </div>
                    <div class="row mb-3">
                        <label class="col-sm-4 col-form-label">Postcode</label>
                        <div class="col-sm-8">
                            <p v-if="!isEditMode" class="form-control-plaintext text-right">{{
                                employer.postcode || 'N/A' }}</p>
                            <input v-else type="text" class="form-control bg-light fw-bold" v-model="employer.postcode" />
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <!-- Change Password -->
        <div class="card border mt-10">
            <div class="card-header">
                <div class="card-title">
                    <h3 class="fw-semibold">Change Password</h3>
                </div>
                <button type="button" class="btn btn-sm btn-secondary align-self-center" @click="openPasswordModal">
                    Update Password
                </button>
            </div>
            <div class="card-body">
                <label class="block col-form-label">Current Password</label>
                <div class="col-md-4">
                    <p class="form-control-plaintext text-right border rounded">********</p>
                </div>
            </div>
        </div>


        <!-- Delete Account -->
        <div class="card border mt-10">
            <div class="card-header">
                <div class="card-title">
                    <h3 class="fw-semibold mt-4">Delete Account</h3>
                </div>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center rounded py-5 px-5 bg-light-warning ">
                    <i class="ki-duotone ki-information-5 fs-3x text-warning me-5"><span class="path1"></span><span class="path2"></span><span class="path3"></span></i> <!--begin::Description-->
                    <div class="text-gray-700 fw-bold fs-6">
                        Custom variables that used to customize Bootstrap components are marked with <code>Custom variable</code> comment in <code>_variables.scss</code>&nbsp; file.
                    </div> <!--end::Description-->
                </div>

            </div>
            <div class="d-flex justify-content-between align-items-center mb-4 mt-10">
                <div class="mx-10">
                    <input type="checkbox" id="confirmDeactivation" class="me-2 cursor-pointer form-check-input me-3" v-model="confirmDeactivation" />
                    <label for="confirmDeactivation" class="me-4">Confirm Account Deactivation</label>
                </div>
                <div>
                    <button type="button" class="btn btn-danger rounded mx-10" :disabled="!confirmDeactivation" @click="deactivateAccount">
                        Deactivate Account
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div v-else class="text-center py-6">
        <p class="text-gray-600">No employer data available.</p>
    </div>

    <!-- Change Password Modal -->
    <div v-if="showPasswordModal" class="modal fade show" tabindex="-1" aria-hidden="true" style="display: block; background-color: rgba(0, 0, 0, 0.5);">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content rounded">
                <div class="modal-header">
                    <h5 class="modal-title">Update Password</h5>
                    <button type="button" class="btn-close" @click="closePasswordModal"></button>
                </div>
                <div class="modal-body">
                    <p class="mb-4">Your password must be a minimum of 8 characters.</p>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="currentPassword" class="form-label">Current Password</label>
                            <input type="password" id="currentPassword" class="form-control bg-light" v-model="password.current" placeholder="Enter current password here" @blur="validateCurrentPassword" />
                            <p v-if="passwordErrors.current" class="text-danger mt-2">{{
                                passwordErrors.current }}</p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="newPassword" class="form-label">New Password</label>
                            <input type="password" id="newPassword" class="form-control bg-light" v-model="password.new" placeholder="Enter new password here" @blur="validateNewPassword" />
                            <p v-if="passwordErrors.new" class="text-danger mt-2">{{ passwordErrors.new
                            }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="confirmPassword" class="form-label">Re-Type Password</label>
                            <input type="password" id="confirmPassword" class="form-control bg-light" v-model="password.confirm" placeholder="Re-type new password here" @blur="validatePasswordMatch" />
                            <p v-if="passwordErrors.confirm" class="text-danger mt-2">{{
                                passwordErrors.confirm }}</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary rounded" @click="savePassword">Save</button>
                </div>
            </div>
        </div>
    </div>

</template>

<script lang="ts">
    import { defineComponent, ref, onMounted } from 'vue';
    import axios from 'axios';
    import Swal from "sweetalert2/dist/sweetalert2.min.js";
    import { Field } from "vee-validate";

    export default defineComponent({
        name: 'EmployerProfile',
        components: {

            Field
        },
        setup() {
            const employer = ref({
                id: '',
                first_name: '',
                last_name: '',
                profile: {
                    position: '',
                    phone: ''
                },
                company_id: '',
                company_name: '',
                email: '',
                state_id: null,
                state: '',
                postcode: '',
            });
            const loading = ref(true);
            const error = ref(null);
            const isEditMode = ref(false);
            const companieslist = ref<{ value: number; label: string }[]>([]);
            const stateslist = ref<{ value: number; label: string }[]>([]);
            const showPasswordModal = ref(false);
            const password = ref({
                current: '',
                new: '',
                confirm: ''
            });
            const passwordErrors = ref({
                current: '',
                new: '',
                confirm: ''
            });
            const isPasswordValid = ref({
                current: false,
                new: false,
                confirm: false
            });
            const confirmDeactivation = ref(false);

            const fetchCompanies = async () => {
                try {
                    const response = await axios.get('/fetchAllCompanies');
                    companieslist.value = response.data.map((item: any) => ({
                        value: item.id,
                        label: item.name
                    }));
                } catch (error) {
                    console.error(error);
                }
            };

            const fetchStates = async () => {
                try {
                    const response = await axios.get('/fetchAllStates');
                    stateslist.value = response.data.map((item: any) => ({
                        value: item.id,
                        label: item.name
                    }));
                } catch (error) {
                    console.error(error);
                }
            };

            const fetchEmployer = async () => {
                try {
                    loading.value = true;
                    await Promise.all([fetchCompanies(), fetchStates()]);
                    const response = await axios.get('/getEmployerDetail');
                    employer.value = {
                        ...response.data,
                        company_id: response.data.company_id,
                        company_name: response.data.company_name,
                        state_id: response.data.state_id,
                        state: response.data.state,
                        profile: response.data.profile || { position: '', phone: '' }
                    };
                    loading.value = false;
                } catch (err: any) {
                    error.value = err.message || 'Failed to load employer data';
                    loading.value = false;
                }
            };

            const toggleEditMode = () => {
                if (isEditMode.value) {
                    updateEmployer();
                }
                isEditMode.value = !isEditMode.value;
            };

            const updateEmployer = async () => {
                try {
                    const payload = {
                        id: employer.value.id,
                        first_name: employer.value.first_name,
                        last_name: employer.value.last_name,
                        profile: {
                            position: employer.value.profile.position,
                            phone: employer.value.profile.phone
                        },
                        company_id: employer.value.company_id,
                        email: employer.value.email,
                        state_id: employer.value.state_id,
                        postcode: employer.value.postcode,
                    };
                    await axios.post(`/employer/${employer.value.id}`, payload);
                    Swal.fire({
                        icon: 'success',
                        title: 'Success',
                        text: 'Employer details updated successfully',
                    }).finally(() => {
                        window.location.reload();
                    });
                } catch (err) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Failed to update employer details',
                    });
                }
            };

            const handleCompanyChange = (selectedCompany: any) => {
                employer.value.company_id = selectedCompany;
            };

            const handleStateChange = (selectedState: any) => {
                employer.value.state_id = selectedState;
            };

            const clearCompanySelection = () => {
                employer.value.company_id = '';
            };

            const clearStateSelection = () => {
                employer.value.state_id = null;
            };

            const openPasswordModal = () => {
                showPasswordModal.value = true;
            };

            const closePasswordModal = () => {
                showPasswordModal.value = false;
            };

            const validateCurrentPassword = async () => {
                if (password.value.current === '') {
                    passwordErrors.value.current = 'Current password is required';
                    isPasswordValid.value.current = false; // Update the `current` property
                    return;
                } else {
                    passwordErrors.value.current = '';
                }

                try {
                    const response = await axios.post(`/employer/${employer.value.id}/verify-password`, {
                        current_password: password.value.current
                    });

                    if (response.data.valid === false) {
                        passwordErrors.value.current = 'Current password is incorrect';
                        isPasswordValid.value.current = false; // Update the `current` property
                    } else {
                        passwordErrors.value.current = '';
                        isPasswordValid.value.current = true; // Update the `current` property
                    }
                } catch (error: any) {
                    if (error.response && error.response.data && error.response.data.error) {
                        passwordErrors.value.current = error.response.data.error; // Display backend error
                    } else {
                        passwordErrors.value.current = 'Error validating password';
                    }
                    isPasswordValid.value.current = false; // Update the `current` property
                }
            };

            const validateNewPassword = () => {
                if (password.value.new.length < 8) {
                    passwordErrors.value.new = 'New password must be at least 8 characters long';
                    isPasswordValid.value.new = false;
                } else {
                    passwordErrors.value.new = '';
                    isPasswordValid.value.new = true;
                }
            };

            const validatePasswordMatch = () => {
                if (password.value.new !== password.value.confirm) {
                    passwordErrors.value.confirm = 'Passwords do not match';
                    isPasswordValid.value.confirm = false; // Update the `confirm` property
                } else {
                    passwordErrors.value.confirm = '';
                    isPasswordValid.value.confirm = true; // Update the `confirm` property
                }
            };

            const savePassword = async () => {
                try {
                    const payload = {
                        current_password: password.value.current,
                        new_password: password.value.new,
                        new_password_confirmation: password.value.confirm
                    };

                    const response = await axios.post(`/employer/${employer.value.id}/update-password`, payload);

                    if (response.data.message) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Success',
                            text: response.data.message,
                        });
                        closePasswordModal();
                    }
                } catch (error: any) {
                    if (error.response && error.response.data && error.response.data.error) {
                        alert(error.response.data.error);
                    } else {
                        alert('Failed to change password');
                    }
                    console.error(error);
                }
            };

            const deactivateAccount = async () => {
                try {
                    const response = await axios.post('/account/deactivate');
                    Swal.fire({
                        icon: 'success',
                        title: 'Success',
                        text: response.data.message,
                    });
                    // Optionally, redirect the user after deactivation
                    window.location.href = '/logout';
                } catch (error: any) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: error.response?.data?.error || 'An error occurred while deactivating the account.',
                    });
                }
            };

            onMounted(() => {
                fetchEmployer();
            });

            return {
                employer,
                loading,
                error,
                isEditMode,
                companieslist,
                stateslist,
                toggleEditMode,
                handleCompanyChange,
                handleStateChange,
                clearCompanySelection,
                clearStateSelection,
                updateEmployer,
                showPasswordModal,
                password,
                openPasswordModal,
                closePasswordModal,
                savePassword,
                validateCurrentPassword,
                validateNewPassword,
                validatePasswordMatch,
                passwordErrors,
                isPasswordValid,
                confirmDeactivation,
                deactivateAccount
            };
        }
    });
</script>

<style scoped>
    .multiselect-wrapper {
        margin-left: -10px
    }

    .col-form-label,
    .form-control-plaintext,
    .form-control {
        color: var(--kt-text-primary);
        font-size: 1.1rem;
    }

    .form-control-plaintext {
        padding: 0.775rem 1rem;
        font-weight: bold;
    }
</style>