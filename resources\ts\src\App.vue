<template>
  <router-view :key="$route.fullPath" />
</template>

<script lang="ts">
import { defineComponent, nextTick, onMounted, ref } from "vue";
import { useStore } from "vuex";
import { Mutations, Actions } from "@/store/enums/StoreEnums";
import { themeMode } from "@/core/helpers/config";
import { initializeComponents } from "@/core/plugins/keenthemes";

export default defineComponent({
  name: "app",
  setup() {
    const store = useStore();

    onMounted(() => {
      /**
       * Overrides the layout config using saved data from localStorage
       * remove this to use static config (@/core/config/DefaultLayoutConfig.ts)
       */
      store.commit(Mutations.OVERRIDE_LAYOUT_CONFIG);
      /**
       *  Sets a mode from configuration
       */
      store.dispatch(Actions.SET_THEME_MODE_ACTION, themeMode.value);

      nextTick(() => {
        initializeComponents();

        store.dispatch(Actions.REMOVE_BODY_CLASSNAME, "page-loading");
      });

      // Create a script element
      const vimeoScript = document.createElement("script");
      // Set the src attribute to your external script URL
      vimeoScript.src = "https://fast.wistia.net/assets/external/E-v1.js";
      // Set the type attribute to 'text/javascript'
      vimeoScript.type = "text/javascript";
      // Append the script element to the document's head
      document.head.appendChild(vimeoScript);
      // Meta Pixel Code
      const metaPixelScript = document.createElement("script");
      metaPixelScript.innerHTML = `
                    !function(f,b,e,v,n,t,s)
                    {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
                    n.callMethod.apply(n,arguments):n.queue.push(arguments)};
                    if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
                    n.queue=[];t=b.createElement(e);t.async=!0;
                    t.src=v;s=b.getElementsByTagName(e)[0];
                    s.parentNode.insertBefore(t,s)}(window,document,'script',
                    'https://connect.facebook.net/en_US/fbevents.js');

                    fbq('init', '1917553959016280');
                    fbq('track', 'PageView');
                    `;
      document.head.appendChild(metaPixelScript);

      const metaPixelNoscript = document.createElement("noscript");
      metaPixelNoscript.innerHTML = `
                    <img height="1" width="1"
                    src="https://www.facebook.com/tr?id=1917553959016280&ev=PageView
                    &noscript=1"/>
                    `;
      document.head.appendChild(metaPixelNoscript);
      // End Meta Pixel Code
    });
  },
});
</script>

<style lang="scss">
@import "bootstrap-icons/font/bootstrap-icons.min.css";
@import "line-awesome/dist/line-awesome/css/line-awesome.min.css";
@import "kit/dist/css/ki.css";
// @import "~apexcharts/dist/apexcharts.css";
// @import "~quill/dist/quill.snow.css";
@import "animate.css";
@import "sweetalert2/dist/sweetalert2.css";
@import "nouislider/dist/nouislider.css";
@import "socicon/css/socicon.css";
// @import "~dropzone/dist/dropzone.css";
@import "@vueform/multiselect/themes/default.css";
// @import "~prism-themes/themes/prism-shades-of-purple.css";

// Main demo style scss
@import "assets/sass/element-ui.dark";
@import "assets/sass/plugins";
@import "assets/sass/style";
@import "@fortawesome/fontawesome-free/css/all.min.css";
@import "./@/assets/css/custom.css";
//RTL version styles
//@import "assets/css/style.rtl.css";

#app {
  display: contents;
}
</style>
