<template>
    <div class="full-view-banner banner" v-bind:style="{ 'backgroundImage': 'url(' + banner.imagefullpath + ')' }">
        <div v-if="banner.video" class="banner-video" v-html="banner.video"></div>
        <div style="position:absolute;width:100%;height:100%;opacity:.3;background:#DFDFDF;"></div>
        <div class="position-absolute bottom-0 p-18 custom-banner-heading mb-18 start-0 text-black w-100">
            <h1 class="fw-normal display-4 mb-2 fs-4x">Pipeline</h1>
            <p class="fs-3" style="font-size: 18px;">Discover and connect with students interested in your industry and
                company</p>
        </div>
    </div>
    <div class="full-view-banner row bg-black black-strip" id="FilteredSection">

    </div>
    <div class="mt-12">
        <div class="filter px-3 pt-3 bg-white rounded shadow-sm">
            <div class="d-flex align-items-center justify-content-between">
                <div class="position-relative w-md-300px me-md-2">
                    <span class="svg-icon svg-icon-3 svg-icon-gray-500 position-absolute top-50 translate-middle ms-6">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1"
                                transform="rotate(45 17.0365 15.1223)" fill="currentColor"></rect>
                            <path
                                d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                                fill="currentColor"></path>
                        </svg>
                    </span>
                    <input v-model="searchQuery" @input="onSearch" class="form-control form-control-solid ps-10"
                        type="text" placeholder="Search Students" autocomplete="off" />
                </div>

                <button type="button" class="btn btn-secondary d-flex align-items-center mt-10"
                    data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
                    Filter by
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="currentColor" width="16" height="16" class="ms-2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="m19.5 8.25-7.5 7.5-7.5-7.5" />
                    </svg>
                </button>
                <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fs-7 w-300px py-4"
                    data-kt-menu="true">
                    <div class="px-7 py-5">
                        <!-- <div class="mb-5">
                            <label
                                class="form-check form-check-custom form-check-solid d-flex align-items-center justify-content-between">
                                <span>Have completed a module</span>
                                <input class="form-check-input ms-2" type="checkbox" v-model="filters.completedModule"
                                    @change="applyFilters">
                            </label>
                        </div> -->


                        <div class="mb-5">
                            <label class="form-label fw-bold">Filter by State</label>
                            <select class="form-select" v-model="filters.state" @change="applyFilters">
                                <option value="">All States</option>
                                <option v-for="state in stateOptions" :key="state.code" :value="state.code">
                                    {{ state.name }}
                                </option>
                            </select>
                        </div>
                        <!-- <div class="mb-5">
                            <label
                                class="form-check form-check-custom form-check-solid d-flex align-items-center justify-content-between">
                                <span>Have engaged with content</span>
                                <input class="form-check-input ms-2" type="checkbox" v-model="filters.engagedContent"
                                    @change="applyFilters">
                            </label>
                        </div>
                        <div class="mb-5">
                            <label
                                class="form-check form-check-custom form-check-solid d-flex align-items-center justify-content-between">
                                <span>Have put their 'industries' in their Game Plan</span>
                                <input class="form-check-input ms-2" type="checkbox"
                                    v-model="filters.industriesInGameplan" @change="applyFilters">
                            </label>
                        </div>
                        <div>
                            <label
                                class="form-check form-check-custom form-check-solid d-flex align-items-center justify-content-between">
                                <span>Have put their 'company' in their Game Plan</span>
                                <input class="form-check-input ms-2" type="checkbox"
                                    v-model="filters.companiesInGameplan" @change="applyFilters">
                            </label>
                        </div> -->
                        <!-- <div class="separator border-gray-200 my-7"></div>
                        <div class="d-flex justify-content-end">
                            <button type="button" class="btn btn-sm btn-primary me-2" @click="resetFilters">Reset</button>
                            <button type="button" class="btn btn-sm btn-light btn-active-light-primary" data-kt-menu-dismiss="true">Close</button>
                        </div> -->
                    </div>
                </div>
            </div>

            <ul class="nav nav-tabs mt-3" id="studentTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="all-students-tab" data-bs-toggle="tab"
                        data-bs-target="#all-students" type="button" role="tab" @click="currentTab = 'all-students'">
                        Following Students
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="following-tab" data-bs-toggle="tab" data-bs-target="#following"
                        type="button" role="tab" @click="currentTab = 'following'">
                        Following Students
                    </button>
                </li>
            </ul>


        </div>


        <div class="d-flex flex-wrap flex-stack py-7">
            <div class="d-flex flex-wrap align-items-center justify-content-center gap-4 my-1">
                <h3 class="fw-bold my-0 text-center">
                    <span v-if="loading">Loading...</span>
                    <span v-else>{{ paginationMeta.total }} Student<span v-if="paginationMeta.total > 1">s</span>
                        Found</span>
                </h3>
                <p class="my-0 text-center text-gray-600" v-if="!loading && currentTab === 'all-students'">
                    {{ paginationMeta.public_count }} Public | {{ paginationMeta.private_count }} Private
                </p>
            </div>
            <div class="d-flex flex-wrap my-1">
                <ul class="nav nav-pills me-6 mb-2 mb-sm-0">
                    <li class="nav-item m-0 text-uppercase d-flex align-items-center pe-4">
                        See students in rows, blocks or map view
                    </li>
                    <li class="nav-item m-0">
                        <a class="btn btn-sm btn-icon btn-light btn-color-muted btn-active-primary me-3 active"
                            data-bs-toggle="tab" href="#kt_project_users_card_pane" @click="currentViewTab = 'card'">
                            <span class="svg-icon svg-icon-2"> <svg xmlns="http://www.w3.org/2000/svg" width="24px"
                                    height="24px" viewBox="0 0 24 24">
                                    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                        <rect x="5" y="5" width="5" height="5" rx="1" fill="currentColor" />
                                        <rect x="14" y="5" width="5" height="5" rx="1" fill="currentColor"
                                            opacity="0.3" />
                                        <rect x="5" y="14" width="5" height="5" rx="1" fill="currentColor"
                                            opacity="0.3" />
                                        <rect x="14" y="14" width="5" height="5" rx="1" fill="currentColor"
                                            opacity="0.3" />
                                    </g>
                                </svg>
                            </span>
                        </a>
                    </li>

                    <li class="nav-item m-0">
                        <a class="btn btn-sm btn-icon btn-light btn-color-muted btn-active-primary me-3"
                            data-bs-toggle="tab" href="#kt_project_users_table_pane" @click="currentViewTab = 'table'">
                            <span class="svg-icon svg-icon-2"> <svg width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M21 7H3C2.4 7 2 6.6 2 6V4C2 3.4 2.4 3 3 3H21C21.6 3 22 3.4 22 4V6C22 6.6 21.6 7 21 7Z"
                                        fill="currentColor" />
                                    <path opacity="0.3"
                                        d="M21 14H3C2.4 14 2 13.6 2 13V11C2 10.4 2.4 10 3 10H21C21.6 10 22 10.4 22 11V13C22 13.6 21.6 14 21 14Z"
                                        fill="currentColor" />
                                    <path opacity="0.3"
                                        d="M21 21H3C2.4 21 2 20.6 2 20V18C2 17.4 2.4 17 3 17H21C21.6 17 22 17.4 22 18V20C22 20.6 21.6 21 21 21Z"
                                        fill="currentColor" />
                                </svg>
                            </span>
                        </a>
                    </li>

                    <li class="nav-item m-0">
                        <a class="btn btn-sm btn-icon btn-light btn-color-muted btn-active-primary" data-bs-toggle="tab"
                            href="#kt_project_users_map_pane" @click="currentViewTab = 'map'">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" id="IconChangeColor"
                                height="16" width="16">
                                <path
                                    d="M168.3 499.2C116.1 435 0 279.4 0 192C0 85.96 85.96 0 192 0C298 0 384 85.96 384 192C384 279.4 267 435 215.7 499.2C203.4 514.5 180.6 514.5 168.3 499.2H168.3zM192 256C227.3 256 256 227.3 256 192C256 156.7 227.3 128 192 128C156.7 128 128 156.7 128 192C128 227.3 156.7 256 192 256z"
                                    id="mainIconPathAttribute" fill="#737373" stroke-width="0.2" stroke="#ff0000">
                                </path>
                            </svg>
                        </a>
                    </li>

                </ul>
            </div>
        </div>

        <div class="tab-content min-h-450px">
            <BlockView :students="students" :loading="loading" />
            <ListView :students="students" :loading="loading" />
            <MapView :search-query="searchQuery" :current-tab="currentTab" :filters="filters" />
        </div>

        <!-- Pagination (only for Block and List views) -->
        <div class="d-flex justify-content-end py-10"
            v-if="!loading && paginationMeta.total > 0 && (currentViewTab === 'card' || currentViewTab === 'table')">
            <TablePagination :total-pages="paginationMeta.last_page" :total="paginationMeta.total"
                :per-page="paginationMeta.per_page" :current-page="paginationMeta.current_page"
                @page-change="onPageChange" />
        </div>

    </div>
</template>

<script lang="ts">
    import { debounce } from 'lodash';
    import { defineComponent, ref, onMounted, watch, nextTick } from 'vue';
    import BlockView from "@/components/Pipeline/BlockView.vue";
    import ListView from "@/components/Pipeline/ListView.vue";
    import MapView from "@/components/Pipeline/MapView.vue";
    import TablePagination from "@/components/kt-datatable/table-partials/table-content/table-footer/TablePagination.vue";
    import { MenuComponent } from "@/assets/ts/components";
    import axios from 'axios';

    interface Student {
        id?: number;
        name?: string;
        email?: string;
        year?: string | null;
        initials?: string | null;
        industriesCount?: number;
        jobsCount?: number;
        companiesCount?: number;
        is_following?: boolean;
        is_public?: boolean;
        location?: string;
    }

    interface PaginationMeta {
        current_page: number;
        from: number;
        last_page: number;
        per_page: number;
        to: number;
        total: number;
        public_count: number;
        private_count: number;
    }

    interface PaginationLinks {
        first: string;
        last: string;
        prev: string | null;
        next: string | null;
    }

    interface StudentsResponse {
        result: { data: Student[] };
        meta: PaginationMeta;
        links: PaginationLinks;
    }



    export default defineComponent({
        components: {
            BlockView,
            ListView,
            MapView,
            TablePagination,
        },
        setup() {
            const banner = ref({
                'trailer_video': null,
                'video': null,
                'imagefullpath': null,
            });
            const students = ref<Student[]>([]);
            const paginationMeta = ref<PaginationMeta>({
                current_page: 1,
                from: 0,
                last_page: 1,
                per_page: 50,
                to: 0,
                total: 0,
                public_count: 0,
                private_count: 0,
            });
            const paginationLinks = ref<PaginationLinks>({
                first: '',
                last: '',
                prev: null,
                next: null,
            });
            const loading = ref(false);
            const searchQuery = ref('');
            const currentTab = ref('all-students');
            const currentViewTab = ref('card');
            const filters = ref({
                completedModule: false,
                engagedContent: false,
                industriesInGameplan: false,
                companiesInGameplan: false,
                state: '',
            });

            const fetchBanner = async () => {
                try {
                    const response = await fetch(
                        'api/getBanner/Employer Pipeline',
                    );

                    const data = await response.json();
                    banner.value = data;
                } catch (error) {
                    console.log(error)
                }
            };

            const fetchStudents = async (page: number = 1, search: string = '', is_following: boolean = false) => {
                loading.value = true;
                try {
                    const params = new URLSearchParams({
                        page: page.toString(),
                        per_page: paginationMeta.value.per_page.toString(),
                    });

                    if (search) {
                        params.append('search', search);
                    }

                    if (is_following) {
                        params.append('is_following', '1');
                    }

                    if (filters.value.completedModule) {
                        params.append('completed_module', '1');
                    }
                    if (filters.value.engagedContent) {
                        params.append('engaged_content', '1');
                    }
                    if (filters.value.industriesInGameplan) {
                        params.append('industries_in_gameplan', '1');
                    }
                    if (filters.value.companiesInGameplan) {
                        params.append('companies_in_gameplan', '1');
                    }
                    if (filters.value.state) {
                        params.append('state', filters.value.state);
                    }

                    let baseUrl = 'employer/get-students';
                    let requestUrl = `${baseUrl}?${params.toString()}`;

                    const response = await axios.get(requestUrl);
                    const data: StudentsResponse = response.data;

                    students.value = data.result.data;
                    paginationMeta.value = data.meta;
                    paginationLinks.value = data.links;

                } catch (error) {
                    console.error('Error fetching students:', error);
                    students.value = [];
                } finally {
                    loading.value = false;
                }
            };

            const stateOptions = ref([]);

            const fetchStates = async () => {
    try {
        const response = await axios.get('/fetchAllStates');
        stateOptions.value = response.data;
    } catch (error) {
        console.error('Failed to load states:', error);
    }
};

            const onPageChange = (page: number) => {
                fetchStudents(page, searchQuery.value, currentTab.value === 'following');
            };

            const onSearch = debounce(() => {
                fetchStudents(1, searchQuery.value, currentTab.value === 'following');
            }, 1000);

            const applyFilters = () => {
                fetchStudents(1, searchQuery.value, currentTab.value === 'following');
            };

            const resetFilters = () => {
                filters.value = {
                    completedModule: false,
                    engagedContent: false,
                    industriesInGameplan: false,
                    companiesInGameplan: false,
                    state: '',

                };
                fetchStudents(1, searchQuery.value, currentTab.value === 'following');
            };




            const scrollToSection = (sectionId: string) => {
                const section = document.getElementById(sectionId);

                if (section) {
                    section.scrollIntoView({ behavior: 'smooth' });
                }
            }

            onMounted(() => {
                fetchBanner();
                fetchStudents();
                fetchStates();
                nextTick(() => {
                    MenuComponent.createInstances('[data-kt-menu="true"]');
                });
            });

            watch(currentTab, (newTab) => {
                if (newTab === 'following') {
                    fetchStudents(1, searchQuery.value, true);
                } else {
                    fetchStudents(1, searchQuery.value, false);
                }
            });



            return {
                banner,
                students,
                paginationMeta,
                paginationLinks,
                loading,
                searchQuery,
                currentTab,
                currentViewTab,
                filters,
                scrollToSection,
                fetchStudents,
                onPageChange,
                onSearch,
                applyFilters,
                resetFilters,
                stateOptions,
            }
        },
    })
</script>

<style>

    .banner {
        background-color: #bbb;
        display: block;
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
        position: relative;
        overflow: hidden;
        min-height: 30vw;
    }

    .full-view-banner {
        margin-left: -30px;
        margin-right: -30px;
    }

    .banner-video {
        height: 100%;
    }

    .banner-video>video {
        /* height: 100%; */
        width: 101% !important;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }


.nav-tabs .nav-link {
    border: none !important;
    color: #A1A5B7
    ;
    background-color: transparent !important;
}

.custom-banner-heading {

    padding: 4rem 3rem 2rem !important;



}

.nav-tabs .nav-link:hover {
    color: #000;
    background-color: transparent !important;
}

.nav-tabs .nav-link.active {
    color: #000;
    font-weight: bold;
    border-bottom: 2px solid #000 !important;
    background-color: transparent !important;
    box-shadow: none !important;
}



    @media (max-width: 1280px) {
        .banner {
            height: 95.25vw;
        }

        .banner_detail_box {
            left: 40%;
        }

        .banner-video>video {
            height: 100% !important;
            width: calc(65vw + 65vh) !important;
        }
    }


    @media (max-width: 991px) {

        .banner {
            height: 99.25vw;
        }


        .full-view-banner,
        .module-sections {
            margin-left: -20px;
            margin-right: -20px;
        }

        .full-view-banner {
            margin-top: 58.16px;
        }

    }



    @media (max-width: 991px) and (min-width: 768px) and (orientation:portrait) {
        .banner {
            height: 95.25vw;
        }

        .banner-video>video {
            height: 100% !important;
            width: calc(66vw + 66vh) !important;
        }
    }

    @media (max-width: 991px) and (orientation:landscape) {

        .banner-video>video {
            height: auto !important;
            width: calc(70vw + 70vh) !important;
        }
    }

    @media (max-width: 767px) {

        .banner {
            height: 110vw;
        }

        .banner_detail_box {
            left: 50%;
        }

    }

    @media (max-width: 575px) {
        .full-view-banner {
            margin-top: 0;
        }

        .banner {
            height: 124vw;
        }

        .custom-banner-heading {

padding: 2rem 2rem !important;


}

        .banner_detail_box {
            width: 70vw !important;
        }

        .banner-video>video .banner {
            height: 150vw;
        }

    }

</style>