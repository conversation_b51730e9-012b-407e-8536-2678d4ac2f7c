<?php

namespace App\Services;

use App\Student;
use App\Employer;
use App\Gameplan;
use App\Lessonresponse;
use App\SkillstrainingResponse;
use App\WorkexperienceResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Overtrue\LaravelFavorite\Favorite;

class EmployerPipelineService
{
    /**
     * Cache duration for pipeline data (5 minutes)
     */
    const CACHE_DURATION = 300;

    /**
     * Get optimized pipeline students query for an employer
     * Uses UNION queries instead of OR conditions for better performance
     *
     * @param Employer $employer
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function getPipelineStudentsQuery(Employer $employer)
    {
        $company = $this->getCompanyWithRelations($employer);

        if (!$company) {
            return Student::whereRaw('1 = 0'); // Return empty query if no company
        }

        // Get company-related IDs (cached for 5 minutes)
        $companyData = $this->getCachedCompanyData($company);

        // Build the main query using UNION for better performance
        $studentIds = $this->getUnionPipelineStudentIds($companyData);

        // Create the main query with the student IDs
        $query = Student::whereIn('id', $studentIds);

        // Apply request-based filters
        $this->applyRequestFilters($query, $employer);

        return $query;
    }

    /**
     * Get cached pipeline student count for dashboard
     *
     * @param Employer $employer
     * @return int
     */
    public function getCachedPipelineCount(Employer $employer): int
    {
        $cacheKey = "employer_pipeline_count_{$employer->id}";

        return Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($employer) {
            return $this->getPipelineStudentsQuery($employer)->count();
        });
    }

    /**
     * Get cached pipeline students with their engaged content
     *
     * @param Employer $employer
     * @return array
     */
    public function getCachedPipelineStudentsWithContent(Employer $employer): array
    {
        $cacheKey = "employer_pipeline_content_{$employer->id}";

        return Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($employer) {
            $company = $this->getCompanyWithRelations($employer);
            if (!$company) {
                return ['student_ids' => [], 'engaged_content' => []];
            }

            $companyData = $this->getCachedCompanyData($company);
            return $this->getUnionPipelineStudentsWithContent($companyData);
        });
    }

    /**
     * Get cached top locations for pipeline students
     *
     * @param Employer $employer
     * @param int $limit
     * @return \Illuminate\Support\Collection
     */
    public function getCachedTopLocations(Employer $employer, int $limit = 3)
    {
        $cacheKey = "employer_pipeline_locations_{$employer->id}_{$limit}";

        return Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($employer, $limit) {
            // Get pipeline student IDs first
            $studentIds = $this->getUnionPipelineStudentIds($this->getCachedCompanyData($this->getCompanyWithRelations($employer)));

            if (empty($studentIds)) {
                return collect();
            }

            // Then get location data for those students
            return DB::table('users')
                ->join('states', 'users.state_id', '=', 'states.id')
                ->selectRaw('states.name as state_name, states.code as state_code, COUNT(users.id) as student_count')
                ->whereIn('users.id', $studentIds)
                ->where('users.role_id', 3) // Student role
                ->groupBy('states.id', 'states.name', 'states.code')
                ->orderByDesc('student_count')
                ->limit($limit)
                ->get();
        });
    }

    /**
     * Get company with necessary relations (cached)
     *
     * @param Employer $employer
     * @return mixed
     */
    private function getCompanyWithRelations(Employer $employer)
    {
        $cacheKey = "employer_company_relations_{$employer->company_id}";

        return Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($employer) {
            return $employer->company()->with([
                'industries',
                'industryunits',
                'workexperienceTemplates',
                'skillstrainingTemplates',
                'lessons'
            ])->first();
        });
    }

    /**
     * Get cached company data IDs
     *
     * @param $company
     * @return array
     */
    private function getCachedCompanyData($company): array
    {
        $cacheKey = "company_data_ids_{$company->id}";

        return Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($company) {
            return [
                'industry_ids' => $company->industries->pluck('id')->toArray(),
                'industryunit_ids' => $company->industryunits->pluck('id')->toArray(),
                'workexperience_template_ids' => $company->workexperienceTemplates->pluck('id')->toArray(),
                'skillstraining_template_ids' => $company->skillstrainingTemplates->pluck('id')->toArray(),
                'lesson_ids' => $company->lessons->pluck('id')->toArray(),
            ];
        });
    }

    /**
     * Get pipeline students with their engaged content using Eloquent models and relationships
     *
     * @param array $companyData
     * @return array
     */
    private function getUnionPipelineStudentsWithContent(array $companyData): array
    {
        $studentIds = [];
        $engagedContentMap = [];

        // Get students from workexperience responses using model relationships
        if (!empty($companyData['workexperience_template_ids'])) {
            $responses = WorkexperienceResponse::with('template:id,title')
                ->select('student_id', 'template_id', 'submitted_at')
                ->whereIn('template_id', $companyData['workexperience_template_ids'])
                ->where('status', 'Submitted')
                ->orderBy('submitted_at', 'desc')
                ->get();

            $this->processEngagementData($responses, $studentIds, $engagedContentMap, 'workexperience', 'template');
        }

        // Get students from skillstraining responses using model relationships
        if (!empty($companyData['skillstraining_template_ids'])) {
            $responses = SkillstrainingResponse::with('template:id,title')
                ->select('student_id', 'template_id', 'submitted_at')
                ->whereIn('template_id', $companyData['skillstraining_template_ids'])
                ->where('status', 'Submitted')
                ->orderBy('submitted_at', 'desc')
                ->get();

            $this->processEngagementData($responses, $studentIds, $engagedContentMap, 'skillstraining', 'template');
        }

        // Get students from lesson responses using model relationships
        if (!empty($companyData['lesson_ids'])) {
            $responses = Lessonresponse::with('lesson:id,title')
                ->select('student_id', 'lesson_id', 'submitted_at')
                ->whereIn('lesson_id', $companyData['lesson_ids'])
                ->where('status', 'Submitted')
                ->orderBy('submitted_at', 'desc')
                ->get();

            $this->processEngagementData($responses, $studentIds, $engagedContentMap, 'lesson', 'lesson');
        }

        // Get students from gameplan industries using model relationships
        if (!empty($companyData['industry_ids'])) {
            $gameplans = Gameplan::with(['industries' => function ($query) use ($companyData) {
                    $query->select('industry_categories.id', 'industry_categories.name')
                          ->whereIn('industry_categories.id', $companyData['industry_ids']);
                }])
                ->select('id', 'user_id', 'created_at')
                ->whereHas('industries', function ($query) use ($companyData) {
                    $query->whereIn('industry_categories.id', $companyData['industry_ids']);
                })
                ->orderBy('created_at', 'desc')
                ->get();

            $this->processGameplanEngagementData($gameplans, $studentIds, $engagedContentMap);
        }

        // Get students from favorites using model relationships
        if (!empty($companyData['industryunit_ids'])) {
            $favorites = Favorite::with('favoriteable:id,title')
                ->select('user_id', 'favoriteable_id', 'created_at')
                ->where('favoriteable_type', 'App\\Industryunit')
                ->whereIn('favoriteable_id', $companyData['industryunit_ids'])
                ->orderBy('created_at', 'desc')
                ->get();

            $this->processFavoriteEngagementData($favorites, $studentIds, $engagedContentMap);
        }

        // Clean up engaged content - get latest per student per type
        $this->cleanupEngagedContent($engagedContentMap);

        return [
            'student_ids' => array_unique($studentIds),
            'engaged_content' => $engagedContentMap
        ];
    }

    /**
     * Process engagement data from response models
     */
    private function processEngagementData($responses, &$studentIds, &$engagedContentMap, $contentType, $relationName)
    {
        $seenStudents = [];

        foreach ($responses as $response) {
            $studentId = $response->student_id;

            // Add to student IDs if not already added
            if (!in_array($studentId, $studentIds)) {
                $studentIds[] = $studentId;
                $engagedContentMap[$studentId] = [];
            }

            // Only take the first (latest) engagement per student for this content type
            if (!isset($seenStudents[$studentId]) && $response->$relationName) {
                $engagedContentMap[$studentId][] = [
                    'title' => $response->$relationName->title,
                    'type' => $contentType,
                    'date' => $response->submitted_at ?? $response->created_at
                ];
                $seenStudents[$studentId] = true;
            }
        }
    }

    /**
     * Process gameplan engagement data
     */
    private function processGameplanEngagementData($gameplans, &$studentIds, &$engagedContentMap)
    {
        $seenStudents = [];

        foreach ($gameplans as $gameplan) {
            $studentId = $gameplan->user_id;

            // Add to student IDs if not already added
            if (!in_array($studentId, $studentIds)) {
                $studentIds[] = $studentId;
                $engagedContentMap[$studentId] = [];
            }

            // Only take the first (latest) gameplan per student
            if (!isset($seenStudents[$studentId]) && $gameplan->industries->isNotEmpty()) {
                $engagedContentMap[$studentId][] = [
                    'title' => $gameplan->industries->first()->name,
                    'type' => 'gameplan_industry',
                    'date' => $gameplan->created_at
                ];
                $seenStudents[$studentId] = true;
            }
        }
    }

    /**
     * Process favorite engagement data
     */
    private function processFavoriteEngagementData($favorites, &$studentIds, &$engagedContentMap)
    {
        $seenStudents = [];

        foreach ($favorites as $favorite) {
            $studentId = $favorite->user_id;

            // Add to student IDs if not already added
            if (!in_array($studentId, $studentIds)) {
                $studentIds[] = $studentId;
                $engagedContentMap[$studentId] = [];
            }

            // Only take the first (latest) favorite per student
            if (!isset($seenStudents[$studentId]) && $favorite->favoriteable) {
                $engagedContentMap[$studentId][] = [
                    'title' => $favorite->favoriteable->title,
                    'type' => 'favorite',
                    'date' => $favorite->created_at
                ];
                $seenStudents[$studentId] = true;
            }
        }
    }

    /**
     * Clean up engaged content to get latest per type and convert to simple array
     */
    private function cleanupEngagedContent(&$engagedContentMap)
    {
        foreach ($engagedContentMap as $studentId => $engagements) {
            $typesSeen = [];
            $cleanEngagements = [];

            // Sort by date descending to get latest first
            usort($engagements, function ($a, $b) {
                return strtotime($b['date']) - strtotime($a['date']);
            });

            // Take only the latest engagement per type
            foreach ($engagements as $engagement) {
                if (!isset($typesSeen[$engagement['type']])) {
                    $cleanEngagements[] = $engagement['title'];
                    $typesSeen[$engagement['type']] = true;
                }
            }

            $engagedContentMap[$studentId] = array_values(array_unique($cleanEngagements));
        }
    }

    /**
     * Legacy method for backward compatibility - now uses the optimized version
     *
     * @param array $companyData
     * @return array
     */
    private function getUnionPipelineStudentIds(array $companyData): array
    {
        $result = $this->getUnionPipelineStudentsWithContent($companyData);
        return $result['student_ids'];
    }

    /**
     * Apply request-based filters to the query
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param Employer $employer
     */
    private function applyRequestFilters($query, Employer $employer)
    {
        // Search filter
        $search = request('search');
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                    ->orWhere('email', 'LIKE', "%{$search}%");
            });
        }

        // State filter
        $state = request('state');
        if ($state) {
            $query->whereHas('state', function ($q) use ($state) {
                $q->where('code', $state);
            });
        }

        // Following filter - only show followed students with public profiles
        $isFollowing = request('is_following');
        if ($isFollowing) {
            $followedStudentIds = $this->getCachedFollowedStudents($employer);
            $query->whereIn('id', $followedStudentIds)
                  ->whereHas('profile', function ($q) {
                      $q->where('is_public', true);
                  });
        }

        // Additional filters for completed modules, engaged content, etc.
        $this->applyAdvancedFilters($query, $employer);
    }

    /**
     * Get cached followed student IDs
     *
     * @param Employer $employer
     * @return array
     */
    private function getCachedFollowedStudents(Employer $employer): array
    {
        $cacheKey = "employer_followed_students_{$employer->id}";

        return Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($employer) {
            return $employer->followedStudents()->pluck('following')->toArray();
        });
    }

    /**
     * Apply advanced filters based on request parameters
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param Employer $employer
     */
    private function applyAdvancedFilters($query, Employer $employer)
    {
        $completedModule = request('completed_module');
        $engagedContent = request('engaged_content');
        $industriesInGameplan = request('industries_in_gameplan');
        $companiesInGameplan = request('companies_in_gameplan');

        if (!($completedModule || $engagedContent || $industriesInGameplan || $companiesInGameplan)) {
            return;
        }

        $company = $this->getCompanyWithRelations($employer);
        $companyData = $this->getCachedCompanyData($company);

        $query->where(function ($q) use ($completedModule, $engagedContent, $industriesInGameplan, $companiesInGameplan, $companyData, $company) {
            if ($completedModule) {
                $q->where(function ($subQuery) {
                    $subQuery->whereHas('workexperienceResponses', function ($q) {
                        $q->where('status', 'Submitted');
                    })->orWhereHas('skillstrainingResponses', function ($q) {
                        $q->where('status', 'Submitted');
                    })->orWhereHas('lessonresponses', function ($q) {
                        $q->where('status', 'Submitted');
                    });
                });
            }

            if ($engagedContent) {
                $q->orWhereHas('favorites', function ($subQ) {
                    $subQ->where('favoriteable_type', 'App\Industryunit');
                });
            }

            if ($industriesInGameplan && !empty($companyData['industry_ids'])) {
                $q->orWhereIn('id', function($subQuery) use ($companyData) {
                    $subQuery->select('g.user_id')
                        ->from('gameplans as g')
                        ->join('gameplan_industries as gi', 'g.id', '=', 'gi.gameplan_id')
                        ->whereIn('gi.industry_category_id', $companyData['industry_ids'])
                        ->whereIn('g.id', function($latestQuery) {
                            $latestQuery->select(DB::raw('MAX(id)'))
                                      ->from('gameplans')
                                      ->groupBy('user_id');
                        });
                });
            }

            if ($companiesInGameplan && $company && $company->detail) {
                $q->orWhereIn('id', function($subQuery) use ($company) {
                    $subQuery->select('g.user_id')
                        ->from('gameplans as g')
                        ->join('gameplan_companies as gc', 'g.id', '=', 'gc.gameplan_id')
                        ->where('gc.company_name', 'LIKE', "%{$company->detail->name}%")
                        ->whereIn('g.id', function($latestQuery) {
                            $latestQuery->select(DB::raw('MAX(id)'))
                                      ->from('gameplans')
                                      ->groupBy('user_id');
                        });
                });
            }
        });
    }

    /**
     * Clear cache when workexperience response changes
     *
     * @param $response
     */
    public function clearCacheForWorkexperienceResponse($response)
    {
        $this->clearCacheForTemplate($response->template_id, 'workexperienceTemplates');
    }

    /**
     * Clear cache when skillstraining response changes
     *
     * @param $response
     */
    public function clearCacheForSkillstrainingResponse($response)
    {
        $this->clearCacheForTemplate($response->template_id, 'skillstrainingTemplates');
    }

    /**
     * Clear cache when lesson response changes
     *
     * @param $response
     */
    public function clearCacheForLessonResponse($response)
    {
        $this->clearCacheForLesson($response->lesson_id);
    }

    /**
     * Clear cache when gameplan industry changes
     *
     * @param $gameplanIndustry
     */
    public function clearCacheForGameplanIndustry($gameplanIndustry)
    {
        $this->clearCacheForIndustry($gameplanIndustry->industry_category_id);
    }

    /**
     * Clear cache when favorite changes
     *
     * @param $favorite
     */
    public function clearCacheForFavorite($favorite)
    {
        if ($favorite->favoriteable_type === 'App\Industryunit') {
            $this->clearCacheForIndustryunit($favorite->favoriteable_id);
        }
    }

    /**
     * Clear cache when user follow changes
     *
     * @param $userFollow
     */
    public function clearCacheForUserFollow($userFollow)
    {
        // Clear cache for the employer who is following
        $employer = Employer::find($userFollow->follow_by);
        if ($employer) {
            $this->clearPipelineCache($employer);
        }
    }

    /**
     * Clear cache for employers whose companies have a specific template
     *
     * @param $templateId
     * @param $relationName
     */
    private function clearCacheForTemplate($templateId, $relationName)
    {
        // Find companies that have this template
        $companyIds = DB::table('company_' . str_replace('Templates', '_templates', $relationName))
            ->where(str_replace('Templates', '_template_id', $relationName), $templateId)
            ->pluck('company_id')
            ->toArray();

        $this->clearCacheForCompanies($companyIds);
    }

    /**
     * Clear cache for employers whose companies have a specific lesson
     *
     * @param $lessonId
     */
    private function clearCacheForLesson($lessonId)
    {
        // Find companies that have this lesson
        $companyIds = DB::table('company_lessons')
            ->where('lesson_id', $lessonId)
            ->pluck('company_id')
            ->toArray();

        $this->clearCacheForCompanies($companyIds);
    }

    /**
     * Clear cache for employers whose companies have a specific industry
     *
     * @param $industryId
     */
    private function clearCacheForIndustry($industryId)
    {
        // Find companies that have this industry
        $companyIds = DB::table('company_industries')
            ->where('industry_category_id', $industryId)
            ->pluck('company_id')
            ->toArray();

        $this->clearCacheForCompanies($companyIds);
    }

    /**
     * Clear cache for employers whose companies have a specific industryunit
     *
     * @param $industryunitId
     */
    private function clearCacheForIndustryunit($industryunitId)
    {
        // Find companies that have this industryunit
        $companyIds = DB::table('company_industryunits')
            ->where('industryunit_id', $industryunitId)
            ->pluck('company_id')
            ->toArray();

        $this->clearCacheForCompanies($companyIds);
    }

    /**
     * Clear cache for all employers of the given companies
     *
     * @param array $companyIds
     */
    private function clearCacheForCompanies(array $companyIds)
    {
        if (empty($companyIds)) {
            return;
        }

        // Find all employers for these companies
        $employers = Employer::whereIn('company_id', $companyIds)->get();

        foreach ($employers as $employer) {
            $this->clearPipelineCache($employer);

            // Also clear the student counts cache
            // Clear multiple possible cache keys (this is a simplified approach)
            for ($i = 0; $i < 100; $i++) {
                Cache::forget("employer_student_counts_{$employer->id}_" . $i);
            }
        }
    }

    /**
     * Clear cache for an employer's pipeline data
     *
     * @param Employer $employer
     */
    public function clearPipelineCache(Employer $employer)
    {
        $patterns = [
            "employer_pipeline_count_{$employer->id}",
            "employer_pipeline_content_{$employer->id}",
            "employer_pipeline_locations_{$employer->id}_*",
            "employer_company_relations_{$employer->company_id}",
            "company_data_ids_{$employer->company_id}",
            "employer_followed_students_{$employer->id}",
        ];

        foreach ($patterns as $pattern) {
            if (strpos($pattern, '*') !== false) {
                // For wildcard patterns, we'd need Redis SCAN or similar
                // For now, just clear the specific keys we know about
                for ($i = 1; $i <= 10; $i++) {
                    Cache::forget(str_replace('*', $i, $pattern));
                }
            } else {
                Cache::forget($pattern);
            }
        }
    }
}
